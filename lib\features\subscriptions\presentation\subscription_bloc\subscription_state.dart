part of 'subscription_bloc.dart';

// States
abstract class SubscriptionState extends Equatable {
  const SubscriptionState();

  @override
  List<Object?> get props => [];
}

class SubscriptionInitial extends SubscriptionState {}

class SubscriptionLoading extends SubscriptionState {}

class SubscriptionProductsLoaded extends SubscriptionState {
  final List<ProductDetails> products;

  const SubscriptionProductsLoaded(this.products);

  @override
  List<Object> get props => [products];
}

class SubscriptionPurchaseInProgress extends SubscriptionState {
  final String productId;

  const SubscriptionPurchaseInProgress(this.productId);

  @override
  List<Object> get props => [productId];
}

class SubscriptionPurchaseValidating extends SubscriptionState {
  final String productId;

  const SubscriptionPurchaseValidating(this.productId);

  @override
  List<Object> get props => [productId];
}

class SubscriptionPurchaseSuccess extends SubscriptionState {
  final String productId;
  final String transactionId;

  const SubscriptionPurchaseSuccess(this.productId, this.transactionId);

  @override
  List<Object> get props => [productId, transactionId];
}

class SubscriptionRestoreSuccess extends SubscriptionState {
  final List<String> restoredProductIds;

  const SubscriptionRestoreSuccess(this.restoredProductIds);

  @override
  List<Object> get props => [restoredProductIds];
}

class SubscriptionError extends SubscriptionState {
  final String message;
  final String? productId;

  const SubscriptionError(this.message, {this.productId});

  @override
  List<Object?> get props => [message, productId];
}
