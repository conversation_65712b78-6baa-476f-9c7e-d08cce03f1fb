import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'food_model.g.dart';

FoodModel foodModelFromJson(dynamic str) => FoodModel.fromJson(str);

@Collection(inheritance: false)
// ignore: must_be_immutable
class FoodModel extends Equatable {
  Id id = Isar.autoIncrement;

  String? dish;
  double? serving;
  String? arabicName;
  String? englishName;
  int? calories;
  double? protein;
  double? carbs;
  double? fat;
  late List<Ingredient> ingredientList;
  bool? isHalal;

  String? imagePath;
  DateTime? date; // Changed from `late DateTime date;` to `DateTime? date;`

  int? healthScore;

  @ignore
  bool isLoading;
  @ignore
  bool hasError;
  @ignore
  String? tempId;

  int? remoteLogId;

  FoodModel({
    this.dish,
    this.calories,
    this.healthScore,
    this.protein,
    this.carbs,
    this.fat,
    this.isHalal,
    this.serving,
    DateTime? date,
    this.imagePath,
    this.isLoading = false,
    this.englishName,
    this.arabicName,
    this.hasError = false,
    this.tempId,
    this.remoteLogId,
    this.ingredientList = const [],
  }) : date = date ?? DateTime.now();

  factory FoodModel.fromDatabaseModel(DatabaseFoodModel db) {
    return FoodModel(
      arabicName: db.arName,
      englishName: db.enName,
      calories: db.calories ?? 0,
      protein: db.protein ?? 0,
      carbs: db.carbs ?? 0,
      fat: db.fat ?? 0,
      isHalal: db.halal,
      imagePath: db.imagePath,
      date: DateTime.now(),
    );
  }

  bool _calculateIsHalal() {
    if (ingredientList.isEmpty) {
      return true;
    }
    return !(ingredientList.any((ingredient) => ingredient.isHalal == false));
  }

  // Method to update isHalal based on ingredients
  void updateHalalStatus() {
    if (ingredientList.isNotEmpty) {
      isHalal = _calculateIsHalal();
    }
  }

  FoodModel copyWith({
    Id? id,
    String? dish,
    double? serving,
    int? calories,
    double? protein,
    double? carbs,
    double? fat,
    bool? isHalal,
    DateTime? date,
    String? imagePath,
    bool? isLoading,
    bool? hasError,
    String? englishName,
    String? arabicName,
    String? tempId,
    int? remoteLogId,
    List<Ingredient>? ingredientList,
    int? healthScore,
    bool updateHalalFromIngredients = false,
  }) {
    final copy = FoodModel(
      dish: dish ?? this.dish,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      isHalal: isHalal ?? this.isHalal,
      date: date ?? this.date,
      imagePath: imagePath ?? this.imagePath,
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      englishName: englishName ?? this.englishName,
      arabicName: arabicName ?? this.arabicName,
      tempId: tempId ?? this.tempId,
      ingredientList: ingredientList ?? this.ingredientList,
      remoteLogId: remoteLogId ?? this.remoteLogId,
      serving: serving ?? this.serving,
      healthScore: healthScore ?? this.healthScore,
    );

    if (updateHalalFromIngredients) {
      copy.isHalal = copy._calculateIsHalal();
    }
    copy.id = id ?? this.id;
    return copy;
  }

  factory FoodModel.fromJson(Map<String, dynamic> fullJson) {
    final json = fullJson['data'] ?? fullJson;

    List<Ingredient> parseIngredients(dynamic ingredientsJson) {
      if (ingredientsJson is List) {
        return ingredientsJson.whereType<Map<String, dynamic>>().map((item) => Ingredient.fromJson(item)).toList();
      }
      return [];
    }

    final ingredients = parseIngredients(json['ingredients']);
    bool? isHalal = json['halal'] as bool?;

    isHalal ??= ingredients.isEmpty ? true : !ingredients.any((i) => i.isHalal == false);

    return FoodModel(
      dish: json['dish'] as String?,
      calories: json['calories'] == null
          ? 0
          : (json['calories'] is int ? json['calories'] as int : int.tryParse(json['calories'].toString()) ?? 0),
      protein: json['protein'] == null ? 0.0 : (json['protein'] is num ? (json['protein'] as num).toDouble() : 0.0),
      carbs: json['carbs'] == null ? 0.0 : (json['carbs'] is num ? (json['carbs'] as num).toDouble() : 0.0),
      fat: json['fats'] == null ? 0.0 : (json['fat'] is num ? (json['fat'] as num).toDouble() : 0.0),
      isHalal: isHalal,
      englishName: json['english_name'] as String? ?? '',
      arabicName: json['arabic_name'] as String? ?? '',
      serving: json['serving'] == null ? null : (json['serving'] is num ? (json['serving'] as num).toDouble() : null),
      healthScore: json['health_score'] == null ? 0 : (json['health_score'] is num ? (json['health_score'] as num).toInt() : 0),
      ingredientList: ingredients,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'dish': dish,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'isHalal': isHalal,
      'date': date?.toIso8601String(),
      'imagePath': imagePath,
      'isLoading': isLoading,
      'english_name': englishName,
      'arabic_name': arabicName,
      'serving': serving,
      'health_score': healthScore,
      'ingredients': ingredientList.map((e) => e.toJson()).toList(),
    };
  }

  @override
  @ignore
  List<Object?> get props => [
        dish,
        calories,
        protein,
        carbs,
        fat,
        isHalal,
        date,
        imagePath,
        hasError,
        isLoading,
        tempId,
        ingredientList,
        serving,
        healthScore,
      ];

  @override
  String toString() {
    return '''
FoodModel(
  id: $id,
  dish: $dish,
  englishName: $englishName,
  arabicName: $arabicName,
  serving: $serving,
  calories: $calories,
  protein: $protein,
  carbs: $carbs,
  fat: $fat,
  healthScore: $healthScore,
  isHalal: $isHalal,
  ingredientList: ${ingredientList.map((e) => e.toString()).toList()},
  imagePath: $imagePath,
  date: $date,
  isLoading: $isLoading,
  hasError: $hasError,
  tempId: $tempId,
  remoteLogId: $remoteLogId
)
''';
  }
}

@embedded
class Ingredient {
  String? englishName;
  String? arabicName;
  int? calories;
  double? protein;
  double? carbs;
  double? fat;
  bool? isHalal;
  String? id;
  String? serving;

  Ingredient({
    this.englishName,
    this.arabicName,
    this.calories,
    this.protein,
    this.carbs,
    this.fat,
    this.isHalal,
    this.id,
    this.serving,
  });

  factory Ingredient.fromJson(Map<String, dynamic> json) {

    int? parseInt(dynamic val) {
      if (val == null) return null;
      if (val is int) return val;
      return int.tryParse(val.toString());
    }

    double? parseDouble(dynamic val) {
      if (val == null) return null;
      if (val is double) return val;
      if (val is int) return val.toDouble();
      return double.tryParse(val.toString());
    }

    // Handle both response formats
    String? englishName;
    String? arabicName;
    bool? isHalal;

    // Format 1: {en_name: "...", ar_name: "...", halal: ...} - detailed format
    if (json.containsKey('en_name') || json.containsKey('ar_name')) {
      englishName = json['en_name'] as String?;
      arabicName = json['ar_name'] as String?;
      isHalal = json['halal'] as bool?;
    }
    // Format 2: {english_name: "...", arabic_name: "...", halal: ...} - ingredients list format
    else {
      englishName = json['english_name'] as String?;
      arabicName = json['arabic_name'] as String?;
      isHalal = json['halal'] as bool?;
    }

    return Ingredient(
      id: json['id'] as String?,
      englishName: englishName,
      arabicName: arabicName,
      calories: parseInt(json['calories']),
      protein: parseDouble(json['protein']),
      carbs: parseDouble(json['carbs']),
      fat: parseDouble(json['fat']),
      isHalal: isHalal,
      serving: json['serving'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'en_name': englishName,
      'ar_name': arabicName,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'halal': isHalal,
      'serving': serving,
    };
  }

  @override
  String toString() {
    return 'Ingredient(id: $id, englishName: $englishName, arabicName: $arabicName, calories: $calories, protein: $protein, carbs: $carbs, fat: $fat, isHalal: $isHalal, serving: $serving)';
  }
}
