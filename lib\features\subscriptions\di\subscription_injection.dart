// import 'package:cal/core/network/http_client.dart';
// import 'package:cal/features/subscriptions/presentation/subscription_bloc/subscription_bloc.dart';
// import 'package:cal/features/subscriptions/services/in_app_purchase_service.dart';
// import 'package:get_it/get_it.dart';
// import 'package:in_app_purchase/in_app_purchase.dart';

// final GetIt sl = GetIt.instance;

// /// Initialize all dependencies for the subscription feature
// /// Call this in your main() function or app initialization

// Future<void> initSubscriptionDependencies() async {
//   sl.registerLazySingleton<InAppPurchase>(
//     () => InAppPurchase.instance,
//   );

//   sl.registerLazySingleton<HTTPClient>(() => sl<HTTPClient>());

//   sl.registerLazySingleton<SubscriptionService>(
//     () => SubscriptionService(
//       inAppPurchase: sl(),
//       dio: sl(),
//     ),
//   );

//   await sl<SubscriptionService>().initialize();

//   sl.registerFactory<SubscriptionBloc>(
//     () => SubscriptionBloc(sl<SubscriptionService>()),
//   );
// }
