import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cal/features/subscriptions/services/in_app_purchase_service.dart';
import 'package:equatable/equatable.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:injectable/injectable.dart';

part 'subscription_event.dart';
part 'subscription_state.dart';

// Enhanced BLoC


@injectable
class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final SubscriptionService _subscriptionService;
  late StreamSubscription<List<PurchaseDetails>> _purchaseStreamSubscription;
  String? _currentPurchasingProductId;

  SubscriptionBloc(this._subscriptionService) : super(SubscriptionInitial()) {
    on<LoadProducts>(_onLoadProducts);
    on<PurchaseProduct>(_onPurchaseProduct);
    on<RestorePurchases>(_onRestorePurchases);
    on<ResetState>(_onResetState);
    on<_PurchaseStreamUpdate>(_onPurchaseStreamUpdate);

    _initializePurchaseStream();
  }

  void _initializePurchaseStream() {
    _purchaseStreamSubscription = InAppPurchase.instance.purchaseStream.listen(
      (List<PurchaseDetails> purchases) {
        add(_PurchaseStreamUpdate(purchases));
      },
      onError: (error) {
        add(const _PurchaseStreamUpdate([]));
      },
    );
  }

  Future<void> _onLoadProducts(
    LoadProducts event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SubscriptionLoading());

    try {
      // // TODO: Remove dummy data - use real service instead
      // // DUMMY DATA FOR UI TESTING - REMOVE THIS BLOCK
      // await Future.delayed(const Duration(seconds: 1)); // Simulate loading
      // final dummyProducts = [
      //   _DummyProductDetails(
      //     id: 'com.yourapp.monthly_premium',
      //     title: 'Monthly Premium',
      //     description: 'Premium features for one month',
      //     price: '\$9.99',
      //     rawPrice: 9.99,
      //     currencyCode: 'USD',
      //   ),
      //   _DummyProductDetails(
      //     id: 'com.yourapp.yearly_premium',
      //     title: 'Yearly Premium',
      //     description: 'Premium features for one year',
      //     price: '\$99.99',
      //     rawPrice: 99.99,
      //     currencyCode: 'USD',
      //   ),
      // ];
      // emit(SubscriptionProductsLoaded(dummyProducts));
      // return;
      // // END DUMMY DATA BLOCK

      if (!_subscriptionService.isAvailable) {
        emit(const SubscriptionError('In-app purchases not available on this device'));
        return;
      }

      final products = await _subscriptionService.loadProducts();

      if (products.isEmpty) {
        emit(const SubscriptionError('No subscription plans available'));
        return;
      }

      emit(SubscriptionProductsLoaded(products));
    } catch (e) {
      emit(SubscriptionError('Failed to load subscription plans: ${e.toString()}'));
    }
  }

  Future<void> _onPurchaseProduct(
    PurchaseProduct event,
    Emitter<SubscriptionState> emit,
  ) async {
    // Prevent multiple simultaneous purchases
    if (_currentPurchasingProductId != null) {
      emit(SubscriptionError('Purchase already in progress', productId: event.productId));
      return;
    }

    _currentPurchasingProductId = event.productId;
    emit(SubscriptionPurchaseInProgress(event.productId));

    try {
      final success = await _subscriptionService.purchaseSubscription(event.productId);

      if (!success) {
        _currentPurchasingProductId = null;
        emit(SubscriptionError('Failed to initiate purchase', productId: event.productId));
      }
      // If success is true, we wait for the purchase stream to handle the actual completion
    } catch (e) {
      _currentPurchasingProductId = null;
      emit(SubscriptionError('Purchase error: ${e.toString()}', productId: event.productId));
    }
  }

  Future<void> _onRestorePurchases(
    RestorePurchases event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SubscriptionLoading());

    try {
      final success = await _subscriptionService.restorePurchases();

      if (!success) {
        emit(const SubscriptionError('Failed to restore purchases'));
      }
      // Wait for purchase stream to handle restored purchases
    } catch (e) {
      emit(SubscriptionError('Restore error: ${e.toString()}'));
    }
  }

  Future<void> _onPurchaseStreamUpdate(
    _PurchaseStreamUpdate event,
    Emitter<SubscriptionState> emit,
  ) async {
    for (final purchase in event.purchases) {
      await _handlePurchaseUpdate(purchase, emit);
    }
  }

  Future<void> _handlePurchaseUpdate(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) async {
    switch (purchase.status) {
      case PurchaseStatus.pending:
        // Keep current state as PurchaseInProgress if this is our current purchase
        if (purchase.productID == _currentPurchasingProductId) {
          emit(SubscriptionPurchaseInProgress(purchase.productID));
        }
        break;

      case PurchaseStatus.purchased:
        await _handleSuccessfulPurchase(purchase, emit);
        break;

      case PurchaseStatus.restored:
        _handleRestoredPurchase(purchase, emit);
        break;

      case PurchaseStatus.error:
        _handlePurchaseError(purchase, emit);
        break;

      case PurchaseStatus.canceled:
        _handlePurchaseCanceled(purchase, emit);
        break;
    }

    // Complete the purchase transaction
    if (purchase.pendingCompletePurchase) {
      await InAppPurchase.instance.completePurchase(purchase);
    }
  }

  Future<void> _handleSuccessfulPurchase(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) async {
    // Only emit validating state if this is our current purchase
    if (purchase.productID == _currentPurchasingProductId) {
      emit(SubscriptionPurchaseValidating(purchase.productID));
    }

    try {
      // Validate with backend
      final isValid = await _subscriptionService.validatePurchaseWithBackend(
        purchase.verificationData.serverVerificationData,
        purchase.productID,
      );

      if (isValid) {
        final transactionId = purchase.purchaseID ?? 'unknown';
        emit(SubscriptionPurchaseSuccess(purchase.productID, transactionId));
      } else {
        emit(SubscriptionError(
          'Purchase validation failed. Please contact support.',
          productId: purchase.productID,
        ));
      }
    } catch (e) {
      emit(SubscriptionError(
        'Failed to validate purchase: ${e.toString()}',
        productId: purchase.productID,
      ));
    } finally {
      if (purchase.productID == _currentPurchasingProductId) {
        _currentPurchasingProductId = null;
      }
    }
  }

  void _handleRestoredPurchase(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) {
    // Apple already verified the purchase - no backend validation needed
    // Just collect all restored product IDs
    final currentState = state;
    List<String> restoredIds = [];

    if (currentState is SubscriptionRestoreSuccess) {
      restoredIds = List.from(currentState.restoredProductIds);
    }

    if (!restoredIds.contains(purchase.productID)) {
      restoredIds.add(purchase.productID);
    }

    emit(SubscriptionRestoreSuccess(restoredIds));
  }

  void _handlePurchaseError(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) {
    final errorMessage = purchase.error?.message ?? 'Unknown purchase error';

    if (purchase.productID == _currentPurchasingProductId) {
      _currentPurchasingProductId = null;
    }

    emit(SubscriptionError(errorMessage, productId: purchase.productID));
  }

  void _handlePurchaseCanceled(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) {
    if (purchase.productID == _currentPurchasingProductId) {
      _currentPurchasingProductId = null;
      // Return to products loaded state if we have products
      if (_subscriptionService.products.isNotEmpty) {
        emit(SubscriptionProductsLoaded(_subscriptionService.products));
      } else {
        emit(SubscriptionInitial());
      }
    }
  }

  void _onResetState(
    ResetState event,
    Emitter<SubscriptionState> emit,
  ) {
    _currentPurchasingProductId = null;
    emit(SubscriptionInitial());
  }

  @override
  Future<void> close() {
    _purchaseStreamSubscription.cancel();
    return super.close();
  }
}
