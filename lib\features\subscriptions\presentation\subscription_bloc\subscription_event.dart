part of 'subscription_bloc.dart';

// Events

abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object> get props => [];
}

class LoadProducts extends SubscriptionEvent {}

class PurchaseProduct extends SubscriptionEvent {
  final String productId;

  const PurchaseProduct(this.productId);

  @override
  List<Object> get props => [productId];
}

class RestorePurchases extends SubscriptionEvent {}

class ResetState extends SubscriptionEvent {}

class _PurchaseStreamUpdate extends SubscriptionEvent {
  final List<PurchaseDetails> purchases;

  const _PurchaseStreamUpdate(this.purchases);

  @override
  List<Object> get props => [purchases];
}

// DUMMY DATA CLASS - REMOVE WHEN USING REAL SERVICE
// ignore: unused_element
class _DummyProductDetails implements ProductDetails {
  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String price;
  @override
  final double rawPrice;
  @override
  final String currencyCode;

  _DummyProductDetails({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.rawPrice,
    required this.currencyCode,
  });
  
  @override
  String get currencySymbol => throw UnimplementedError();
}
